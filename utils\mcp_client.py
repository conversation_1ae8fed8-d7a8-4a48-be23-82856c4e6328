import json
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
from loguru import logger
from pydantic import BaseModel
import config


class MCPTool(BaseModel):
    """MCP工具定义"""
    name: str
    description: str
    inputSchema: Dict[str, Any]


class MCPServer(BaseModel):
    """MCP服务器配置"""
    name: str
    type: str
    description: str
    isActive: bool
    baseUrl: str
    tools: List[MCPTool] = []


class MCPClient:
    """MCP客户端，用于与MCP服务进行通信"""
    
    def __init__(self):
        self.servers: Dict[str, MCPServer] = {}
        self.session: Optional[aiohttp.ClientSession] = None
        self._initialized = False
    
    async def initialize(self):
        """初始化MCP客户端"""
        if self._initialized:
            return
            
        self.session = aiohttp.ClientSession()
        
        # 加载配置的MCP服务器
        for server_id, server_config in config.MCP_SERVERS.items():
            if server_config.get("isActive", False):
                server = MCPServer(**server_config)
                self.servers[server_id] = server
                
                # 获取服务器的工具列表
                await self._fetch_tools(server_id, server)
        
        self._initialized = True
        logger.info(f"MCP客户端初始化完成，加载了 {len(self.servers)} 个服务器")
    
    async def _fetch_tools(self, server_id: str, server: MCPServer):
        """获取MCP服务器的工具列表"""
        try:
            url = f"{server.baseUrl}/tools/list"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    tools_data = data.get("tools", [])
                    tools = [MCPTool(**tool) for tool in tools_data]
                    server.tools = tools
                    logger.info(f"从服务器 {server.name} 获取到 {len(tools)} 个工具")
                else:
                    logger.error(f"获取服务器 {server.name} 工具列表失败: {response.status}")
        except Exception as e:
            logger.error(f"获取服务器 {server.name} 工具列表异常: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            # 查找包含该工具的服务器
            target_server = None
            for server in self.servers.values():
                for tool in server.tools:
                    if tool.name == tool_name:
                        target_server = server
                        break
                if target_server:
                    break
            
            if not target_server:
                return {
                    "success": False,
                    "error": f"未找到工具: {tool_name}"
                }
            
            # 调用工具
            url = f"{target_server.baseUrl}/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"工具 {tool_name} 调用成功")
                    return {
                        "success": True,
                        "result": result
                    }
                else:
                    error_text = await response.text()
                    logger.error(f"工具 {tool_name} 调用失败: {response.status} - {error_text}")
                    return {
                        "success": False,
                        "error": f"工具调用失败: {response.status}"
                    }
                    
        except Exception as e:
            logger.error(f"工具 {tool_name} 调用异常: {str(e)}")
            return {
                "success": False,
                "error": f"工具调用异常: {str(e)}"
            }
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取所有可用的工具列表，格式化为OpenAI function calling格式"""
        tools = []
        for server in self.servers.values():
            for tool in server.tools:
                tools.append({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.inputSchema
                    }
                })
        return tools
    
    async def close(self):
        """关闭MCP客户端"""
        if self.session and not self.session.closed:
            await self.session.close()
        self._initialized = False


# 全局MCP客户端实例
mcp_client = MCPClient()


async def get_mcp_client() -> MCPClient:
    """获取MCP客户端实例"""
    if not mcp_client._initialized:
        await mcp_client.initialize()
    return mcp_client
