import re
import json
import traceback
from openai import AsyncOpenAI
import config
from pydantic import BaseModel
from typing import List, Dict, Any
from loguru import logger
from .mcp_client import get_mcp_client

def get_think_and_answer(content: str) -> tuple[str, str]:
    think_pattern = r'<think>(.*?)</think>'
    think_match = re.search(think_pattern, content, re.DOTALL)

    if think_match:
        # 提取reasoning_content
        reasoning_content = think_match.group(1).strip()
        # 从内容中删除<think></think>块
        answer = re.sub(think_pattern, '', content, flags=re.DOTALL).strip()
    else:
        reasoning_content = ''
        answer = content.strip()

    return reasoning_content, answer


class LLMResponse(BaseModel):
    success: bool
    reasoning_content: str
    answer: str
    prompt_tokens: int
    completion_tokens: int
    all_tokens: int



def get_client() -> AsyncOpenAI:
    """
    获取异步OpenAI客户端实例。
    该函数从配置文件中读取API密钥和基础URL，并返回一个AsyncOpenAI客户端实例。
    """
    client = AsyncOpenAI(
        api_key=config.OPENAI_API_KEY,  # 从配置文件读取API密钥
        base_url=config.OPENAI_BASE_URL  # 从配置文件读取基础URL
    )
    return client


async def get_llm_response(messages: list) -> LLMResponse:
    """
    使用指定的模型和消息列表获取LLM响应，支持MCP工具调用。
    """
    try:
        # 获取MCP客户端和可用工具
        mcp_client = await get_mcp_client()
        available_tools = mcp_client.get_available_tools()

        # 获取异步OpenAI客户端
        client = get_client()

        # 准备请求参数
        request_params = {
            "model": config.OPENAI_MODEL,
            "messages": messages,
            "temperature": config.TEMPERATURE,
            "max_tokens": config.MAX_TOKENS,
            "stream": False
        }

        # 如果有可用工具，添加到请求中
        if available_tools:
            request_params["tools"] = available_tools
            request_params["tool_choice"] = "auto"

        # 异步调用获取响应
        response = await client.chat.completions.create(**request_params)

        if response and response.choices:
            message = response.choices[0].message
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
            all_tokens = response.usage.total_tokens

            # 检查是否有工具调用
            if message.tool_calls:
                # 处理工具调用
                tool_results = []
                for tool_call in message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)

                    logger.info(f"调用工具: {function_name}, 参数: {function_args}")

                    # 调用MCP工具
                    tool_result = await mcp_client.call_tool(function_name, function_args)
                    tool_results.append({
                        "tool_call_id": tool_call.id,
                        "function_name": function_name,
                        "result": tool_result
                    })

                # 将工具调用结果添加到消息历史中
                messages.append({
                    "role": "assistant",
                    "content": None,
                    "tool_calls": message.tool_calls
                })

                # 添加工具调用结果
                for tool_result in tool_results:
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_result["tool_call_id"],
                        "content": json.dumps(tool_result["result"], ensure_ascii=False)
                    })

                # 再次调用LLM获取最终响应
                final_response = await client.chat.completions.create(
                    model=config.OPENAI_MODEL,
                    messages=messages,
                    temperature=config.TEMPERATURE,
                    max_tokens=config.MAX_TOKENS,
                    stream=False
                )

                if final_response and final_response.choices:
                    content = final_response.choices[0].message.content.strip()
                    reasoning_content, answer = get_think_and_answer(content)

                    # 累加token使用量
                    prompt_tokens += final_response.usage.prompt_tokens
                    completion_tokens += final_response.usage.completion_tokens
                    all_tokens += final_response.usage.total_tokens

                    result = LLMResponse(
                        success=True,
                        reasoning_content=reasoning_content,
                        answer=answer,
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                        all_tokens=all_tokens
                    )
                else:
                    result = LLMResponse(
                        success=False,
                        reasoning_content="",
                        answer="工具调用后未获得有效响应",
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                        all_tokens=all_tokens
                    )
            else:
                # 没有工具调用，直接处理响应
                content = message.content.strip()
                reasoning_content, answer = get_think_and_answer(content)

                result = LLMResponse(
                    success=True,
                    reasoning_content=reasoning_content,
                    answer=answer,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    all_tokens=all_tokens
                )
        else:
            result = LLMResponse(
                success=False,
                reasoning_content="",
                answer="No response from the model.",
                prompt_tokens=0,
                completion_tokens=0,
                all_tokens=0
            )

    except Exception as e:
        traceback.print_exc()
        result = LLMResponse(
            success=False,
            reasoning_content="",
            answer=f"Error: {str(e)}",
            prompt_tokens=0,
            completion_tokens=0,
            all_tokens=0
        )

    return result