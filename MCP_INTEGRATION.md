# MCP 集成说明

本项目已集成 MCP (Model Context Protocol) 服务，允许大模型在对话过程中调用外部工具。

## 功能特性

- ✅ 自动发现和加载 MCP 服务器的工具
- ✅ 在对话中智能调用相关工具
- ✅ 支持多个 MCP 服务器
- ✅ 工具调用结果自动整合到对话中
- ✅ Web 界面显示可用工具列表

## 配置说明

### MCP 服务器配置

在 `config.py` 中配置你的 MCP 服务器：

```python
MCP_SERVERS = {
    "5piePcWGhnCp9ujjl4rqZ": {
        "name": "商品查询",
        "type": "sse",
        "description": "商品查询",
        "isActive": True,
        "baseUrl": "http://127.0.0.1:8888/mcp"
    }
}
```

### 配置参数说明

- `name`: 服务器显示名称
- `type`: 连接类型（目前支持 "sse"）
- `description`: 服务器描述
- `isActive`: 是否启用该服务器
- `baseUrl`: MCP 服务器的基础 URL

## API 端点

### 获取可用工具

```
GET /api/mcp/tools
```

返回所有可用的 MCP 工具列表。

### 聊天接口

```
POST /api/chat
```

原有的聊天接口，现在支持自动工具调用。

## 使用方法

### 1. 启动 MCP 服务

确保你的 MCP 服务正在运行：
```bash
# 示例：启动商品查询服务
# 你的 MCP 服务应该在 http://127.0.0.1:8888/mcp 上运行
```

### 2. 启动 AI Chat 服务

```bash
# 安装依赖
pip install -r requirements.txt

# 启动服务
python main_openai.py
```

### 3. 测试 MCP 集成

运行测试脚本：
```bash
python test_mcp.py
```

### 4. 在 Web 界面中使用

1. 打开 http://localhost:8000
2. 输入 API 令牌
3. 点击"查看可用工具"按钮查看已加载的工具
4. 在对话中询问相关问题，AI 会自动调用合适的工具

## 工具调用示例

当用户询问商品相关问题时，AI 会自动调用商品查询工具：

```
用户: 请帮我查询一下商品信息
AI: [自动调用商品查询工具] 根据查询结果，为您提供以下商品信息...
```

## 故障排除

### 1. MCP 服务连接失败

- 检查 MCP 服务是否正在运行
- 确认 `baseUrl` 配置正确
- 查看服务日志中的错误信息

### 2. 工具调用失败

- 确认工具参数格式正确
- 检查 MCP 服务的工具实现
- 查看详细的错误日志

### 3. 工具未被调用

- 确认问题描述足够明确
- 检查工具的描述是否准确
- 尝试更具体的问题表述

## 开发说明

### 添加新的 MCP 服务器

1. 在 `config.py` 中添加新的服务器配置
2. 重启应用，系统会自动加载新服务器的工具
3. 测试工具调用功能

### 自定义工具调用逻辑

主要的工具调用逻辑在 `utils/llm.py` 中的 `get_llm_response` 函数中实现。

### MCP 客户端扩展

MCP 客户端实现在 `utils/mcp_client.py` 中，可以根据需要扩展功能。

## 日志和监控

- 工具调用会记录在应用日志中
- 可以通过日志监控工具调用的成功率和性能
- 错误信息会详细记录便于调试

## 注意事项

1. 确保 MCP 服务的稳定性和可用性
2. 工具调用会增加响应时间和 token 消耗
3. 建议对工具调用进行适当的错误处理和重试机制
4. 注意保护敏感信息，避免在工具调用中泄露
