#!/usr/bin/env python3
"""
MCP集成测试脚本
"""
import asyncio
import json
from utils.mcp_client import get_mcp_client
from utils.llm import get_llm_response


async def test_mcp_client():
    """测试MCP客户端基本功能"""
    print("=== 测试MCP客户端 ===")
    
    try:
        # 获取MCP客户端
        client = await get_mcp_client()
        print(f"✓ MCP客户端初始化成功")
        
        # 获取可用工具
        tools = client.get_available_tools()
        print(f"✓ 获取到 {len(tools)} 个可用工具:")
        for tool in tools:
            function_info = tool.get("function", {})
            print(f"  - {function_info.get('name')}: {function_info.get('description')}")
        
        return True
        
    except Exception as e:
        print(f"✗ MCP客户端测试失败: {str(e)}")
        return False


async def test_llm_with_tools():
    """测试LLM与工具集成"""
    print("\n=== 测试LLM工具调用 ===")
    
    try:
        # 构造一个可能触发工具调用的消息
        messages = [
            {
                "role": "system",
                "content": "你是一个智能助手，可以帮助用户查询商品信息。当用户询问商品相关问题时，请使用可用的工具来获取准确信息。"
            },
            {
                "role": "user", 
                "content": "请帮我查询一下商品信息"
            }
        ]
        
        print("发送消息到LLM...")
        response = await get_llm_response(messages)
        
        if response.success:
            print("✓ LLM响应成功")
            print(f"回答: {response.answer}")
            if response.reasoning_content:
                print(f"推理过程: {response.reasoning_content}")
            print(f"Token使用: {response.all_tokens}")
        else:
            print(f"✗ LLM响应失败: {response.answer}")
            
        return response.success
        
    except Exception as e:
        print(f"✗ LLM工具调用测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("开始MCP集成测试...\n")
    
    # 测试MCP客户端
    mcp_success = await test_mcp_client()
    
    # 测试LLM集成
    llm_success = await test_llm_with_tools()
    
    print(f"\n=== 测试结果 ===")
    print(f"MCP客户端: {'✓ 通过' if mcp_success else '✗ 失败'}")
    print(f"LLM集成: {'✓ 通过' if llm_success else '✗ 失败'}")
    
    if mcp_success and llm_success:
        print("\n🎉 所有测试通过！MCP集成成功！")
    else:
        print("\n❌ 部分测试失败，请检查配置和服务状态")


if __name__ == "__main__":
    asyncio.run(main())
